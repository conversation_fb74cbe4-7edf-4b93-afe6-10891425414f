<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!--
  **  This is the J2EE Web Services Client 1.1 XSD with only the required elements to support an implementation.
  **  Please see http://java.sun.com/xml/ns/j2ee/j2ee_web_services_client_1_1.xsd for a fully documented and latest
  **  XSD. 
-->

<xsd:schema xmlns="http://www.w3.org/2001/XMLSchema" 
            targetNamespace="http://java.sun.com/xml/ns/j2ee" 
            xmlns:j2ee="http://java.sun.com/xml/ns/j2ee" 
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
            elementFormDefault="qualified" 
            attributeFormDefault="unqualified" version="1.1">
    <xsd:complexType name="port-component-refType">
        <xsd:sequence>
            <xsd:element name="service-endpoint-interface" type="j2ee:fully-qualified-classType" />
            <xsd:element name="port-component-link" type="j2ee:string" minOccurs="0" maxOccurs="1" />
        </xsd:sequence>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

    <xsd:group name="service-refGroup">
        <xsd:sequence>
            <xsd:element name="service-ref" type="j2ee:service-refType" minOccurs="0" maxOccurs="unbounded">
                <xsd:key name="service-ref_handler-name-key">
                    <xsd:selector xpath="j2ee:handler" />
                    <xsd:field xpath="j2ee:handler-name" />
                </xsd:key>
            </xsd:element>
        </xsd:sequence>
    </xsd:group>

    <xsd:complexType name="service-refType">
        <xsd:sequence>
            <xsd:group ref="j2ee:descriptionGroup" />
            <xsd:element name="service-ref-name" type="j2ee:jndi-nameType" />

            <xsd:element name="service-interface" type="j2ee:fully-qualified-classType" />

            <xsd:element name="wsdl-file" type="j2ee:xsdAnyURIType" minOccurs="0" maxOccurs="1" />

            <xsd:element name="jaxrpc-mapping-file" type="j2ee:pathType" minOccurs="0" maxOccurs="1" />

            <xsd:element name="service-qname" type="j2ee:xsdQNameType" minOccurs="0" maxOccurs="1" />

            <xsd:element name="port-component-ref" type="j2ee:port-component-refType" minOccurs="0" maxOccurs="unbounded" />

            <xsd:element name="handler" type="j2ee:service-ref_handlerType" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

    <xsd:complexType name="service-ref_handlerType">
        <xsd:sequence>
            <xsd:group ref="j2ee:descriptionGroup" />
            <xsd:element name="handler-name" type="j2ee:string" />
            <xsd:element name="handler-class" type="j2ee:fully-qualified-classType" />
            <xsd:element name="init-param" type="j2ee:param-valueType" minOccurs="0" maxOccurs="unbounded" />

            <xsd:element name="soap-header" type="j2ee:xsdQNameType" minOccurs="0" maxOccurs="unbounded" />

            <xsd:element name="soap-role" type="j2ee:string" minOccurs="0" maxOccurs="unbounded" />

            <xsd:element name="port-name" type="j2ee:string" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

</xsd:schema>
