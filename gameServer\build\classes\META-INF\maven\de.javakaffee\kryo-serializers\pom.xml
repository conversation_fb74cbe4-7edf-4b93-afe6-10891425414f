<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<!-- To be able to deploy snapshots/releases to sonatype / maven central: https://docs.sonatype.org/display/Repository/Sonatype+OSS+Maven+Repository+Usage+Guide -->
	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>7</version>
	</parent>

	<groupId>de.javakaffee</groupId>
	<artifactId>kryo-serializers</artifactId>
	<version>0.45</version>
	<packaging>bundle</packaging>
	<name>kryo serializers</name>
	<description>
    Additional kryo (http://kryo.googlecode.com) serializers for standard jdk types (e.g. currency, jdk proxies) and some for external libs (e.g. joda time, cglib proxies, wicket).
    </description>
	<url>https://github.com/magro/kryo-serializers</url>

	<issueManagement>
		<system>github.com</system>
		<url>https://github.com/magro/kryo-serializers/issues</url>
	</issueManagement>
	<inceptionYear>2010</inceptionYear>
	<prerequisites>
		<maven>2.0.9</maven>
	</prerequisites>
	<licenses>
		<license>
			<name>The Apache Software License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<scm>
		<connection>scm:git:**************:magro/kryo-serializers.git</connection>
		<developerConnection>scm:git:**************:magro/kryo-serializers.git</developerConnection>
		<url>**************:magro/kryo-serializers.git</url>
	</scm>
	<developers>
		<developer>
			<id>martin.grotzke</id>
			<name>Martin Grotzke</name>
			<email><EMAIL></email>
			<roles>
				<role>owner</role>
				<role>developer</role>
			</roles>
			<timezone>+1</timezone>
		</developer>
	</developers>
	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<inherited>true</inherited>
					<configuration>
						<source>1.7</source>
						<target>1.7</target>
						<encoding>utf-8</encoding>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>2.4.0</version>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<Import-Package><![CDATA[
						com.esotericsoftware.kryo*;version="[5.0.0,)",
						com.esotericsoftware.minlog*;version="[1.2,2.0)",
						sun.reflect;resolution:=optional,
						*
						]]>
						</Import-Package>
					</instructions>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestEntries>
							<Automatic-Module-Name>de.javakaffee.kryoserializers</Automatic-Module-Name>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.10.3</version>
				<configuration>
					<!-- required for java8, so that javadoc errors don't fail the build -->
					<additionalparam>-Xdoclint:none</additionalparam>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>cobertura-maven-plugin</artifactId>
				<version>2.7</version>
				<configuration>
					<formats>
						<format>html</format>
						<format>xml</format>
					</formats>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.eluder.coveralls</groupId>
				<artifactId>coveralls-maven-plugin</artifactId>
				<version>4.1.0</version>
			</plugin>
		</plugins>
	</build>

	<reporting>
		<excludeDefaults>true</excludeDefaults>
		<plugins>
			<plugin>
				<artifactId>maven-project-info-reports-plugin</artifactId>
				<version>2.4</version>
				<reportSets>
					<reportSet>
						<id>sunlink</id>
						<reports>
							<report>javadoc</report>
						</reports>
						<inherited>true</inherited>
						<configuration>
							<links>
								<link>http://download.oracle.com/javase/7/docs/api/</link>
							</links>
						</configuration>
					</reportSet>
				</reportSets>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jxr-plugin</artifactId>
				<version>2.2</version>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>findbugs-maven-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<findbugsXmlOutput>true</findbugsXmlOutput>
					<threshold>Normal</threshold> <!-- High|Normal|Low|Exp|Ignore -->
					<effort>Max</effort><!-- Min|Default|Max -->
					<onlyAnalyze>de.javakaffee.kryoserializers.-</onlyAnalyze>
					<debug>false</debug>
				</configuration>
			</plugin>
		</plugins>
	</reporting>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<slf4j.version>1.5.8</slf4j.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>4.7</version>
				<scope>test</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
        <!--
          kryo jars were uploaded as 3rd party jars following
          https://docs.sonatype.org/display/Repository/Uploading+3rd-party+Artifacts+to+Maven+Central
          this also required groupId com.googlecode (instead of com.esotericsoftware) due to project url
        -->
		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
			<version>5.0.0-RC1</version>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>3.6.1</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>1.6.2</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
			<version>3.1</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.wicket</groupId>
			<artifactId>wicket</artifactId>
			<version>1.4.17</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>17.0</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.github.andrewoma.dexx</groupId>
			<artifactId>collection</artifactId>
			<version>0.6</version>
			<optional>true</optional>
		</dependency>

		<!-- Testing -->
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>6.8.21</version>
			<scope>test</scope>
		</dependency>

		<!-- Required for CGLibProxySerializerTest -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<!-- for tests with WicketTester -->
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>test</scope>
		</dependency>
		<!-- by WicketTester required but not pulled in -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<version>${slf4j.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<!-- test with some Integer subtype -->
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.4</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<repositories>
		<!-- Required for TestNG -->
		<repository>
			<id>jcenter</id>
			<name>bintray</name>
			<url>http://jcenter.bintray.com</url>
		</repository>

		<!--
		  kryo jars were uploaded as 3rd party jars following
		  https://docs.sonatype.org/display/Repository/Uploading+3rd-party+Artifacts+to+Maven+Central
		  and are now available in maven central
		-->
	</repositories>
</project>
