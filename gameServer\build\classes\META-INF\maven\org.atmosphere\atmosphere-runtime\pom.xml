<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.atmosphere</groupId>
        <artifactId>atmosphere-project</artifactId>
        <version>2.6.4</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.atmosphere</groupId>
    <artifactId>atmosphere-runtime</artifactId>
    <packaging>bundle</packaging>
    <version>2.6.4</version>
    <name>atmosphere-runtime</name>
    <url>https://github.com/Atmosphere/atmosphere</url>
    <build>
        <defaultGoal>install</defaultGoal>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/java/</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>${felix-version}</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Import-Package>
                            com.sun*;resolution:=optional,
                            org.glassfish.grizzly*;resolution:=optional,
                            org.apache.catalina*;resolution:=optional;version="[6.0,9)",
                            org.apache.tomcat*;resolution:=optional;version="[6.0,9)",
                            org.eclipse.jetty.continuation;resolution:=optional;version="[7.6,10)",
                            org.eclipse.jetty.websocket;resolution:=optional;version="[7.6,10)",
                            org.eclipse.jetty*;resolution:=optional,
                            org.jboss.servlet*;resolution:=optional,
                            org.apache.coyote*;resolution:=optional,
                            org.mortbay.util.ajax*;resolution:=optional,
                            org.atmosphere.inject*;resolution:=optional,
                            org.atmosphere.jboss*;resolution:=optional,
                            org.apache.shiro*;resolution:=optional,
                            org.jboss.vfs*;resolution:=optional,
                            javax.servlet*;version="${servlet-version-range}",
                            javax.websocket*;resolution:=optional,
                            javax.enterprise*;resolution:=optional,
                            javax.inject*;resolution:=optional,
                            weblogic.websocket*;resolution:=optional,
                            *,
                        </Import-Package>
                        <Export-Package>
                            org.atmosphere.cache*,
                            org.atmosphere.client*,
                            org.atmosphere.config*,
                            org.atmosphere.config.managed*,
                            org.atmosphere.config.service*,
                            org.atmosphere.container*,
                            org.atmosphere.cpr*,
                            org.atmosphere.inject*,
                            org.atmosphere.inject.annotation*,
                            org.atmosphere.handler*,
                            org.atmosphere.interceptor*,
                            org.atmosphere.pool*,
                            org.atmosphere.util*,
                            org.atmosphere.websocket*,
                            org.atmosphere.lifecycle*,
                            org.atmosphere.websocket.protocol*,
                        </Export-Package>
                        <Require-Capability>
                            osgi.extender;filter:="(osgi.extender=osgi.serviceloader.processor)",
                            osgi.extender;filter:="(osgi.extender=osgi.serviceloader.registrar)",
                            osgi.serviceloader;filter:="(osgi.serviceloader=org.atmosphere.inject.Injectable)";cardinality:=multiple
                        </Require-Capability>
                        <Provide-Capability>
                            osgi.serviceloader;osgi.serviceloader=javax.servlet.ServletContainerInitializer,
                            osgi.serviceloader;osgi.serviceloader=org.atmosphere.inject.CDIProducer,
                            osgi.serviceloader;osgi.serviceloader=org.atmosphere.inject.Injectable
                        </Provide-Capability>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-servlet_3.0_spec</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>websocket-server</artifactId>
            <version>${jetty9_3-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-io</artifactId>
            <version>${jetty9-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-util</artifactId>
            <version>${jetty9-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-continuation</artifactId>
            <version>${jetty7-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.glassfish.grizzly</groupId>
            <artifactId>grizzly-comet</artifactId>
            <version>${grizzly2-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.glassfish.grizzly</groupId>
            <artifactId>grizzly-websockets</artifactId>
            <version>${grizzly2-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.glassfish.grizzly</groupId>
            <artifactId>grizzly-http-servlet</artifactId>
            <version>${grizzly2-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.sun.grizzly</groupId>
            <artifactId>grizzly-websockets</artifactId>
            <version>${grizzly-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.sun.grizzly</groupId>
            <artifactId>grizzly-framework-http</artifactId>
            <version>${grizzly10-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sun.grizzly</groupId>
            <artifactId>grizzly-comet</artifactId>
            <version>${grizzly-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.grizzly</groupId>
            <artifactId>grizzly-compat</artifactId>
            <version>${grizzly-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.atmosphere.jboss.as</groupId>
            <artifactId>jboss-as-websockets</artifactId>
            <version>${jbossaswebsockets-version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss</groupId>
            <artifactId>jboss-vfs</artifactId>
            <version>3.1.0.Final</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.atmosphere</groupId>
            <artifactId>atmosphere-compat-jbossweb</artifactId>
            <version>${compat-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.atmosphere</groupId>
            <artifactId>atmosphere-compat-tomcat</artifactId>
            <version>${compat-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.atmosphere</groupId>
            <artifactId>atmosphere-compat-tomcat7</artifactId>
            <version>${compat-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-catalina</artifactId>
            <version>${tomcat7-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-coyote</artifactId>
            <version>${tomcat7-version}</version>
            <scope>provided</scope>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>1.8.5</version>
            <type>jar</type>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>${shiro-version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.atmosphere</groupId>
            <artifactId>atmosphere-stubs</artifactId>
            <version>1.0.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <version>6.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-web</artifactId>
            <version>${shiro-version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20180130</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-websocket</artifactId>
            <version>7.6.10.v20130312</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
    
