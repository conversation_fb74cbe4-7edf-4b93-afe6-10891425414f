<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- ======================================================= -->
    <!--   Artifact Coordinates                                  -->
    <!-- ======================================================= -->
    <parent>
        <groupId>org.javolution</groupId>
        <artifactId>javolution</artifactId>
        <version>6.0.0</version>
    </parent>
    <artifactId>javolution-core-java</artifactId>
    <packaging>bundle</packaging>
    <name>Javolution Core (Java)</name>

    <!-- ======================================================= -->
    <!--   OSGi Dependencies                                     -->
    <!-- ======================================================= -->
    <dependencies>
        <dependency> <!-- OSGI Core Library -->
            <groupId>org.osgi</groupId>
            <artifactId>org.osgi.core</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency> <!-- OSGI Compendium (log service) -->
            <groupId>org.osgi</groupId>
            <artifactId>org.osgi.compendium</artifactId>
            <version>4.3.1</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <!-- ======================================================= -->
            <!--     OSGi Packaging                                      -->
            <!-- ======================================================= -->
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.3.7</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Export-Package>!javolution.*.internal.*,javolution.*,org.osgi.service.log</Export-Package>
                        <Private-Package>javolution.*.internal.*,javax.realtime.*</Private-Package>
                        <Bundle-Activator>javolution.osgi.internal.JavolutionActivator</Bundle-Activator>
                        <Export-Service>javolution.xml.stream.XMLInputFactory,
                                        javolution.xml.stream.XMLOutputFactory</Export-Service>
                    </instructions>
                </configuration>
            </plugin>
            
            <!-- ======================================================= -->
            <!--     Source code packaging (for IDE)                     -->
            <!-- ======================================================= -->  
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- ======================================================= -->
            <!--     Custom Javadoc plugin (for reports)                 -->
            <!-- ======================================================= -->
            <plugin>
                <groupId>org.javolution</groupId>
                <artifactId>colapi</artifactId>
                <version>2.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>colorize</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
  
            <!-- ======================================================= -->
            <!--     Javadoc packaging  (for IDE)                        -->
            <!-- ======================================================= -->  
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                   <sourcepath>${project.build.directory}/colorized</sourcepath>
                   <outputDirectory>${project.build.directory}/apidocs</outputDirectory>
                   <excludePackageNames>javax.*,*.internal</excludePackageNames>
                   <overview>${project.build.directory}/colorized/javolution/doc-files/overview.html</overview>
                   <docfilessubdirs>true</docfilessubdirs>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
        </plugins>
    </build>
</project>