##
## Licensed to the Apache Software Foundation (ASF) under one
## or more contributor license agreements.  See the NOTICE file
## distributed with this work for additional information
## regarding copyright ownership.  The ASF licenses this file
## to you under the Apache License, Version 2.0 (the
## "License"); you may not use this file except in compliance
## with the License.  You may obtain a copy of the License at
##
##  http://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing,
## software distributed under the License is distributed on an
## "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
## KIND, either express or implied.  See the License for the
## specific language governing permissions and limitations
## under the License.
##

##
## $Rev: 600957 $ $Date: 2007-12-04 09:01:31 -0500 (Tue, 04 Dec 2007) $
##

# Default localized string information
# Localized for Locale en_US

err.cookie_name_is_token=Cookie name \"{0}\" is a reserved token
err.io.negativelength=Negative Length given in write method
err.io.short_read=Short Read

http.method_not_implemented=Method {0} is not defined in RFC 2068 and is not supported by the Servlet API 

http.method_get_not_supported=HTTP method GET is not supported by this URL
http.method_post_not_supported=HTTP method POST is not supported by this URL
http.method_put_not_supported=HTTP method PUT is not supported by this URL
http.method_delete_not_supported=Http method DELETE is not supported by this URL
