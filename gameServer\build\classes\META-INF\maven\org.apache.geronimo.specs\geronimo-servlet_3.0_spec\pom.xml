<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<!-- $Rev: 936727 $ $Date: 2010-04-22 06:14:34 -0400 (Thu, 22 Apr 2010) $ -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache.geronimo.genesis</groupId>
        <artifactId>genesis-java5-flava</artifactId>
        <version>2.0</version>
    </parent>

    <groupId>org.apache.geronimo.specs</groupId>
    <artifactId>geronimo-servlet_3.0_spec</artifactId>
    <packaging>bundle</packaging>
    <name>Servlet 3.0</name>
    <version>1.0</version>

    <description>Servlet 3.0 API</description>

    <url>http://geronimo.apache.org/maven/${siteId}/${version}</url>

    <distributionManagement>
        <site>
            <id>apache-website</id>
            <url>${site.deploy.url}/maven/${siteId}/${version}</url>
        </site>
    </distributionManagement>

    <properties>
        <siteId>specs/${artifactId}</siteId>
    </properties>

    <scm>
        <connection>scm:svn:https://svn.apache.org/repos/asf/geronimo/specs/tags/geronimo-servlet_3.0_spec-1.0</connection>
        <developerConnection>scm:svn:https://svn.apache.org/repos/asf/geronimo/specs/tags/geronimo-servlet_3.0_spec-1.0</developerConnection>
        <url>http://svn.apache.org/viewcvs.cgi/geronimo/specs/tags/geronimo-servlet_3.0_spec-1.0</url>
    </scm>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <!-- The OSGi Alliance defined package numbers don't match up
                             necessarily with the Java EE versions.  Servlet 3.0 needs to
                             be exported as 2.6 -->
                        <Export-Package>
                            javax.servlet;version=2.6,
                            javax.servlet.annotation;version=2.6,
                            javax.servlet.descriptor;version=2.6,
                            javax.servlet.http;version=2.6,
                            javax.servlet.resources;version=2.6,
                            javax.servlet;version=3.0,
                            javax.servlet.annotation;version=3.0,
                            javax.servlet.descriptor;version=3.0,
                            javax.servlet.http;version=3.0,
                            javax.servlet.resources;version=3.0,
                        </Export-Package>
                        <!-- Because of the duplicate exports, explicit imports are required -->
                        <Import-Package>
                            javax.servlet;version=2.6,
                            javax.servlet.annotation;version=2.6,
                            javax.servlet.descriptor;version=2.6,
                            javax.servlet.http;version=2.6,
                            javax.servlet.resources;version=2.6,
                        </Import-Package>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>rat</id>
            <build>
                <plugins>
                    <plugin>
                        <!-- Override of default rat checks.  To use, type "mvn -Prat verify". -->
                        <groupId>org.apache.rat</groupId>
                        <artifactId>apache-rat-plugin</artifactId>
                        <version>0.6</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <reportFile>${project.build.directory}/${project.build.finalName}.rat</reportFile>
                            <excludeSubProjects>false</excludeSubProjects>
                            <excludes>
                                <exclude>**/target/**/*</exclude>
                                <exclude>**/appended-resources/**/*</exclude>
                                <exclude>**/velocity.log</exclude>
                                <!-- manifest files don't support comments so don't contain the ASL2.0 header -->
                                <exclude>**/*.MF</exclude>
                                <!-- These are covered by information in the LICENSE file -->
                                <exclude>**/xml.xsd</exclude>
                                <exclude>**/XMLSchema.dtd</exclude>
                                <!--RAT doesn't seem to recognize MIT style licenses-->
                                <exclude>manual/src/styles/print.css</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
