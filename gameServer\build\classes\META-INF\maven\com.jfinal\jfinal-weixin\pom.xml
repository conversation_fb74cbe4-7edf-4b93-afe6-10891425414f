<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.jfinal</groupId>
  <artifactId>jfinal-weixin</artifactId>
  <version>1.9</version>
  <name>jfinal-weixin</name>
  <url>http://www.jfinal.com</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <jdk.version>1.6</jdk.version>
  </properties>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <scm>
    <connection>scm:git:*******************:jfinal/jfinal-weixin.git</connection>
    <developerConnection>scm:git:*******************:jfinal/jfinal-weixin.git</developerConnection>
    <url>*******************:jfinal/jfinal-weixin.git</url>
  </scm>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.8.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.jfinal</groupId>
      <artifactId>jetty-server</artifactId>
      <version>8.1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.jfinal</groupId>
      <artifactId>jfinal</artifactId>
      <version>3.1</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.4.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.31</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>2.7.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>de.ruedigermoeller</groupId>
      <artifactId>fst</artifactId>
      <version>2.29</version>
      <scope>provided</scope>
    </dependency>
    <!-- okhttp2 -->
    <dependency>
      <groupId>com.squareup.okhttp</groupId>
      <artifactId>okhttp</artifactId>
      <version>2.7.5</version>
      <scope>provided</scope>
    </dependency>
    <!-- okhttp3 -->
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.6.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okio</groupId>
      <artifactId>okio</artifactId>
      <version>1.11.0</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <finalName>jfinal-weixin</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>2.1.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.3.2</version>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <excludes>
            <exclude>a_little_config.txt</exclude>
            <exclude>a_little_config_pro.txt</exclude>
            <exclude>ehcache.xml</exclude>
            <exclude>log4j.properties</exclude>
            <exclude>com/jfinal/weixin/demo</exclude>
            <exclude>com/jfinal/weixin/demo/*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <source>${jdk.version}</source>
          <target>${jdk.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.3</version>
        <configuration>
          <encoding>${project.build.sourceEncoding}</encoding>
          <outputDirectory>${basedir}</outputDirectory>
          <reportOutputDirectory>${basedir}</reportOutputDirectory>
          <excludePackageNames>com.jfinal.weixin.demo</excludePackageNames>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <!-- 使用aliyun maven -->
  <repositories>
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public</url>
    </repository>
  </repositories>

  <profiles>
    <profile>
      <!-- 本地开发环境 -->
      <id>dev</id>
      <properties>
        <profiles.active>dev</profiles.active>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <!-- mvn clean deploy -P release -Dgpg.passphrase=xxxx -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- GPG -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.5</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <snapshotRepository>
          <id>oss</id>
          <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
          <id>oss</id>
          <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
      </distributionManagement>
    </profile>
  </profiles>
</project>