<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!--
  **  This is the J2EE Web Services 1.1 XSD with only the required elements to support an implementation.
  **  Please see http://java.sun.com/xml/ns/j2ee/j2ee_web_services_1_1.xsd for a fully documented and latest
  **  XSD. 
-->

<xsd:schema xmlns="http://www.w3.org/2001/XMLSchema" 
            targetNamespace="http://java.sun.com/xml/ns/j2ee" 
            xmlns:j2ee="http://java.sun.com/xml/ns/j2ee" 
            xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
            elementFormDefault="qualified" 
            attributeFormDefault="unqualified" version="1.1">

    <xsd:include schemaLocation="j2ee_1_4.xsd" />

    <xsd:element name="webservices" type="j2ee:webservicesType">
        <xsd:key name="webservice-description-name-key">
            <xsd:selector xpath="j2ee:webservice-description" />
            <xsd:field xpath="j2ee:webservice-description-name" />
        </xsd:key>
    </xsd:element>

    <xsd:complexType name="port-componentType">
        <xsd:sequence>
            <xsd:element name="description" type="j2ee:descriptionType" minOccurs="0" maxOccurs="1" />
            <xsd:element name="display-name" type="j2ee:display-nameType" minOccurs="0" maxOccurs="1" />
            <xsd:element name="icon" type="j2ee:iconType" minOccurs="0" maxOccurs="1" />
            <xsd:element name="port-component-name" type="j2ee:string" />
            <xsd:element name="wsdl-port" type="j2ee:xsdQNameType" />
            <xsd:element name="service-endpoint-interface" type="j2ee:fully-qualified-classType" />
            <xsd:element name="service-impl-bean" type="j2ee:service-impl-beanType" />
            <xsd:element name="handler" type="j2ee:port-component_handlerType" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

    <xsd:complexType name="port-component_handlerType">
        <xsd:sequence>
            <xsd:group ref="j2ee:descriptionGroup" />
            <xsd:element name="handler-name" type="j2ee:string" />
            <xsd:element name="handler-class" type="j2ee:fully-qualified-classType" />
            <xsd:element name="init-param" type="j2ee:param-valueType" minOccurs="0" maxOccurs="unbounded" />
            <xsd:element name="soap-header" type="j2ee:xsdQNameType" minOccurs="0" maxOccurs="unbounded" />
            <xsd:element name="soap-role" type="j2ee:string" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

    <xsd:complexType name="service-impl-beanType">
        <xsd:choice>
            <xsd:element name="ejb-link" type="j2ee:ejb-linkType" />
            <xsd:element name="servlet-link" type="j2ee:servlet-linkType" />
        </xsd:choice>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

    <xsd:complexType name="servlet-linkType">
        <xsd:simpleContent>
            <xsd:restriction base="j2ee:string" />
        </xsd:simpleContent>
    </xsd:complexType>

    <xsd:complexType name="webservice-descriptionType">
        <xsd:sequence>
            <xsd:element name="description" type="j2ee:descriptionType" minOccurs="0" maxOccurs="1" />
            <xsd:element name="display-name" type="j2ee:display-nameType" minOccurs="0" maxOccurs="1" />
            <xsd:element name="icon" type="j2ee:iconType" minOccurs="0" maxOccurs="1" />
            <xsd:element name="webservice-description-name" type="j2ee:string" />
            <xsd:element name="wsdl-file" type="j2ee:pathType" />
            <xsd:element name="jaxrpc-mapping-file" type="j2ee:pathType" />
            <xsd:element name="port-component" type="j2ee:port-componentType" minOccurs="1" maxOccurs="unbounded">
                <xsd:key name="port-component_handler-name-key">
                    <xsd:selector xpath="j2ee:handler" />
                    <xsd:field xpath="j2ee:handler-name" />
                </xsd:key>
            </xsd:element>
        </xsd:sequence>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

    <xsd:complexType name="webservicesType">
        <xsd:sequence>
            <xsd:group ref="j2ee:descriptionGroup" />
            <xsd:element name="webservice-description" type="j2ee:webservice-descriptionType" minOccurs="1" maxOccurs="unbounded">
                <xsd:key name="port-component-name-key">
                    <xsd:selector xpath="j2ee:port-component" />
                    <xsd:field xpath="j2ee:port-component-name" />
                </xsd:key>
            </xsd:element>
        </xsd:sequence>

        <xsd:attribute name="version" type="j2ee:dewey-versionType" fixed="1.1" use="required"></xsd:attribute>
        <xsd:attribute name="id" type="xsd:ID" />
    </xsd:complexType>

</xsd:schema>
