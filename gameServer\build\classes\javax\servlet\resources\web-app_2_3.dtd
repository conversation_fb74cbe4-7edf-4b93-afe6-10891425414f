<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<!ELEMENT web-app (icon?, display-name?, description?, distributable?,
context-param*, filter*, filter-mapping*, listener*, servlet*,
servlet-mapping*, session-config?, mime-mapping*, welcome-file-list?,
error-page*, taglib*, resource-env-ref*, resource-ref*, security-constraint*,
login-config?, security-role*, env-entry*, ejb-ref*,  ejb-local-ref*)>

<!ELEMENT auth-constraint (description?, role-name*)>

<!ELEMENT auth-method (#PCDATA)>

<!ELEMENT context-param (param-name, param-value, description?)>

<!ELEMENT description (#PCDATA)>

<!ELEMENT display-name (#PCDATA)>

<!ELEMENT distributable EMPTY>

<!ELEMENT ejb-link (#PCDATA)>

<!ELEMENT ejb-local-ref (description?, ejb-ref-name, ejb-ref-type,
		local-home, local, ejb-link?)>

<!ELEMENT ejb-ref (description?, ejb-ref-name, ejb-ref-type,
		home, remote, ejb-link?)>

<!ELEMENT ejb-ref-name (#PCDATA)>

<!ELEMENT ejb-ref-type (#PCDATA)>

<!ELEMENT env-entry (description?, env-entry-name, env-entry-value?,
env-entry-type)>

<!ELEMENT env-entry-name (#PCDATA)>

<!ELEMENT env-entry-type (#PCDATA)>

<!ELEMENT env-entry-value (#PCDATA)>

<!ELEMENT error-code (#PCDATA)>

<!ELEMENT error-page ((error-code | exception-type), location)>

<!ELEMENT exception-type (#PCDATA)>

<!ELEMENT extension (#PCDATA)>

<!ELEMENT filter (icon?, filter-name, display-name?, description?,
filter-class, init-param*)>

<!ELEMENT filter-class (#PCDATA)>

<!ELEMENT filter-mapping (filter-name, (url-pattern | servlet-name))>

<!ELEMENT filter-name (#PCDATA)>

<!ELEMENT form-error-page (#PCDATA)>

<!ELEMENT form-login-config (form-login-page, form-error-page)>

<!ELEMENT form-login-page (#PCDATA)>

<!ELEMENT home (#PCDATA)>

<!ELEMENT http-method (#PCDATA)>

<!ELEMENT icon (small-icon?, large-icon?)>

<!ELEMENT init-param (param-name, param-value, description?)>

<!ELEMENT jsp-file (#PCDATA)>

<!ELEMENT large-icon (#PCDATA)>

<!ELEMENT listener (listener-class)>

<!ELEMENT listener-class (#PCDATA)>

<!ELEMENT load-on-startup (#PCDATA)>

<!ELEMENT local (#PCDATA)>

<!ELEMENT local-home (#PCDATA)>

<!ELEMENT location (#PCDATA)>

<!ELEMENT login-config (auth-method?, realm-name?, form-login-config?)>

<!ELEMENT mime-mapping (extension, mime-type)>

<!ELEMENT mime-type (#PCDATA)>

<!ELEMENT param-name (#PCDATA)>

<!ELEMENT param-value (#PCDATA)>

<!ELEMENT realm-name (#PCDATA)>

<!ELEMENT remote (#PCDATA)>

<!ELEMENT res-auth (#PCDATA)>

<!ELEMENT res-ref-name (#PCDATA)>

<!ELEMENT res-sharing-scope (#PCDATA)>

<!ELEMENT res-type (#PCDATA)>

<!ELEMENT resource-env-ref (description?, resource-env-ref-name,
		resource-env-ref-type)>

<!ELEMENT resource-env-ref-name (#PCDATA)>

<!ELEMENT resource-env-ref-type (#PCDATA)>

<!ELEMENT resource-ref (description?, res-ref-name, res-type, res-auth,
		res-sharing-scope?)>

<!ELEMENT role-link (#PCDATA)>

<!ELEMENT role-name (#PCDATA)>

<!ELEMENT run-as (description?, role-name)>

<!ELEMENT security-constraint (display-name?, web-resource-collection+,
auth-constraint?, user-data-constraint?)>

<!ELEMENT security-role (description?, role-name)>

<!ELEMENT security-role-ref (description?, role-name, role-link?)>

<!ELEMENT servlet (icon?, servlet-name, display-name?, description?,
(servlet-class|jsp-file), init-param*, load-on-startup?, run-as?, security-role-ref*)>

<!ELEMENT servlet-class (#PCDATA)>

<!ELEMENT servlet-mapping (servlet-name, url-pattern)>

<!ELEMENT servlet-name (#PCDATA)>

<!ELEMENT session-config (session-timeout?)>

<!ELEMENT session-timeout (#PCDATA)>

<!ELEMENT small-icon (#PCDATA)>

<!ELEMENT taglib (taglib-uri, taglib-location)>

<!ELEMENT taglib-location (#PCDATA)>

<!ELEMENT taglib-uri (#PCDATA)>

<!ELEMENT transport-guarantee (#PCDATA)>

<!ELEMENT url-pattern (#PCDATA)>

<!ELEMENT user-data-constraint (description?, transport-guarantee)>

<!ELEMENT web-resource-collection (web-resource-name, description?,
url-pattern*, http-method*)>

<!ELEMENT web-resource-name (#PCDATA)>

<!ELEMENT welcome-file (#PCDATA)>

<!ELEMENT welcome-file-list (welcome-file+)>

<!ATTLIST auth-constraint id ID #IMPLIED>
<!ATTLIST auth-method id ID #IMPLIED>
<!ATTLIST context-param id ID #IMPLIED>
<!ATTLIST description id ID #IMPLIED>
<!ATTLIST display-name id ID #IMPLIED>
<!ATTLIST distributable id ID #IMPLIED>
<!ATTLIST ejb-link id ID #IMPLIED>
<!ATTLIST ejb-local-ref id ID #IMPLIED>
<!ATTLIST ejb-ref id ID #IMPLIED>
<!ATTLIST ejb-ref-name id ID #IMPLIED>
<!ATTLIST ejb-ref-type id ID #IMPLIED>
<!ATTLIST env-entry id ID #IMPLIED>
<!ATTLIST env-entry-name id ID #IMPLIED>
<!ATTLIST env-entry-type id ID #IMPLIED>
<!ATTLIST env-entry-value id ID #IMPLIED>
<!ATTLIST error-code id ID #IMPLIED>
<!ATTLIST error-page id ID #IMPLIED>
<!ATTLIST exception-type id ID #IMPLIED>
<!ATTLIST extension id ID #IMPLIED>
<!ATTLIST filter id ID #IMPLIED>
<!ATTLIST filter-class id ID #IMPLIED>
<!ATTLIST filter-mapping id ID #IMPLIED>
<!ATTLIST filter-name id ID #IMPLIED>
<!ATTLIST form-error-page id ID #IMPLIED>
<!ATTLIST form-login-config id ID #IMPLIED>
<!ATTLIST form-login-page id ID #IMPLIED>
<!ATTLIST home id ID #IMPLIED>
<!ATTLIST http-method id ID #IMPLIED>
<!ATTLIST icon id ID #IMPLIED>
<!ATTLIST init-param id ID #IMPLIED>
<!ATTLIST jsp-file id ID #IMPLIED>
<!ATTLIST large-icon id ID #IMPLIED>
<!ATTLIST listener id ID #IMPLIED>
<!ATTLIST listener-class id ID #IMPLIED>
<!ATTLIST load-on-startup id ID #IMPLIED>
<!ATTLIST local id ID #IMPLIED>
<!ATTLIST local-home id ID #IMPLIED>
<!ATTLIST location id ID #IMPLIED>
<!ATTLIST login-config id ID #IMPLIED>
<!ATTLIST mime-mapping id ID #IMPLIED>
<!ATTLIST mime-type id ID #IMPLIED>
<!ATTLIST param-name id ID #IMPLIED>
<!ATTLIST param-value id ID #IMPLIED>
<!ATTLIST realm-name id ID #IMPLIED>
<!ATTLIST remote id ID #IMPLIED>
<!ATTLIST res-auth id ID #IMPLIED>
<!ATTLIST res-ref-name id ID #IMPLIED>
<!ATTLIST res-sharing-scope id ID #IMPLIED>
<!ATTLIST res-type id ID #IMPLIED>
<!ATTLIST resource-env-ref id ID #IMPLIED>
<!ATTLIST resource-env-ref-name id ID #IMPLIED>
<!ATTLIST resource-env-ref-type id ID #IMPLIED>
<!ATTLIST resource-ref id ID #IMPLIED>
<!ATTLIST role-link id ID #IMPLIED>
<!ATTLIST role-name id ID #IMPLIED>
<!ATTLIST run-as id ID #IMPLIED>
<!ATTLIST security-constraint id ID #IMPLIED>
<!ATTLIST security-role id ID #IMPLIED>
<!ATTLIST security-role-ref id ID #IMPLIED>
<!ATTLIST servlet id ID #IMPLIED>
<!ATTLIST servlet-class id ID #IMPLIED>
<!ATTLIST servlet-mapping id ID #IMPLIED>
<!ATTLIST servlet-name id ID #IMPLIED>
<!ATTLIST session-config id ID #IMPLIED>
<!ATTLIST session-timeout id ID #IMPLIED>
<!ATTLIST small-icon id ID #IMPLIED>
<!ATTLIST taglib id ID #IMPLIED>
<!ATTLIST taglib-location id ID #IMPLIED>
<!ATTLIST taglib-uri id ID #IMPLIED>
<!ATTLIST transport-guarantee id ID #IMPLIED>
<!ATTLIST url-pattern id ID #IMPLIED>
<!ATTLIST user-data-constraint id ID #IMPLIED>
<!ATTLIST web-app id ID #IMPLIED>
<!ATTLIST web-resource-collection id ID #IMPLIED>
<!ATTLIST web-resource-name id ID #IMPLIED>
<!ATTLIST welcome-file id ID #IMPLIED>
<!ATTLIST welcome-file-list id ID #IMPLIED>
